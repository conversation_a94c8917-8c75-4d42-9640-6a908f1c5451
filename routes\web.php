<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\BusController;
use App\Http\Controllers\TripController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\SeatController;
use App\Models\City;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    $cities = City::orderBy('name')->get();
    return view('welcome', compact('cities'));
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Public trip search
Route::get('/search', [TripController::class, 'search'])->name('trips.search');
Route::get('/trips/{trip}/seats', [TripController::class, 'selectSeats'])->name('trips.select-seats');
Route::post('/trips/{trip}/book', [BookingController::class, 'store'])->name('trips.book.store');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Bus management routes
    Route::resource('buses', BusController::class);
    Route::get('buses/{bus}/seats', [SeatController::class, 'index'])->name('buses.seats.index');
    Route::get('buses/{bus}/seats/create', [SeatController::class, 'create'])->name('buses.seats.create');
    Route::post('buses/{bus}/seats', [SeatController::class, 'store'])->name('buses.seats.store');
    Route::get('buses/{bus}/seats/{seat}', [SeatController::class, 'show'])->name('buses.seats.show');
    Route::get('buses/{bus}/seats/{seat}/edit', [SeatController::class, 'edit'])->name('buses.seats.edit');
    Route::put('buses/{bus}/seats/{seat}', [SeatController::class, 'update'])->name('buses.seats.update');
    Route::delete('buses/{bus}/seats/{seat}', [SeatController::class, 'destroy'])->name('buses.seats.destroy');
    Route::get('buses/{bus}/seat-availability', [SeatController::class, 'availability'])->name('buses.seats.availability');

    // Trip management routes
    Route::resource('trips', TripController::class);
    Route::get('trips/{trip}/seat-availability', [TripController::class, 'seatAvailability'])->name('trips.seat-availability');
    Route::post('trips/{trip}/cancel', [TripController::class, 'cancel'])->name('trips.cancel');

    // Booking routes
    Route::resource('bookings', BookingController::class)->except(['edit', 'update']);
    Route::get('trips/{trip}/book', [BookingController::class, 'create'])->name('trips.book');
    Route::post('trips/{trip}/book', [BookingController::class, 'store'])->name('trips.book.store');
    Route::get('bookings/{booking}/payment', [BookingController::class, 'payment'])->name('bookings.payment');
    Route::get('bookings/{booking}/confirmation', [BookingController::class, 'confirmation'])->name('bookings.confirmation');
    Route::post('bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');

    // Payment routes
    Route::resource('payments', PaymentController::class)->only(['index', 'show']);
    Route::post('bookings/{booking}/payment', [PaymentController::class, 'process'])->name('payments.process');
    Route::get('payments/{payment}/callback', [PaymentController::class, 'callback'])->name('payments.callback');
    Route::post('payments/{payment}/confirm-cash', [PaymentController::class, 'confirmCash'])->name('payments.confirm-cash');
});

require __DIR__.'/auth.php';
