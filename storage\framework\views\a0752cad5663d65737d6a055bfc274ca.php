<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('My Bookings')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <?php if(session('success')): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__empty_1 = true; $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                                <div class="p-6">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm font-medium text-indigo-600"><?php echo e($booking->booking_reference); ?></p>
                                            <h3 class="text-lg font-semibold text-gray-900 mt-1">
                                                <?php echo e($booking->trip->route->fromCity->name); ?> to <?php echo e($booking->trip->route->toCity->name); ?>

                                            </h3>
                                        </div>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php switch($booking->status):
                                                case ('confirmed'): ?> bg-green-100 text-green-800 <?php break; ?>
                                                <?php case ('pending'): ?> bg-yellow-100 text-yellow-800 <?php break; ?>
                                                <?php case ('cancelled'): ?> bg-red-100 text-red-800 <?php break; ?>
                                                <?php default: ?> bg-gray-100 text-gray-800
                                            <?php endswitch; ?>
                                        ">
                                            <?php echo e(ucfirst($booking->status)); ?>

                                        </span>
                                    </div>
                                    <div class="mt-4">
                                        <p class="text-sm text-gray-600">
                                            <strong>Date:</strong> <?php echo e($booking->trip->departure_datetime->format('d M Y, H:i')); ?>

                                        </p>
                                        <p class="text-sm text-gray-600">
                                            <strong>Bus:</strong> <?php echo e($booking->trip->bus->bus_name); ?> (<?php echo e($booking->trip->bus->bus_reg_number); ?>)
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            <strong>Passenger:</strong> <?php echo e($booking->passenger_name); ?>

                                        </p>
                                    </div>
                                </div>
                                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
                                    <a href="<?php echo e(route('bookings.show', $booking)); ?>" class="text-sm font-medium text-indigo-600 hover:text-indigo-900">View Details</a>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="md:col-span-2 lg:col-span-3 text-center py-12">
                                <p class="text-gray-500 text-lg">You have no bookings.</p>
                                <a href="<?php echo e(url('/')); ?>" class="mt-4 inline-block bg-indigo-600 text-white font-bold py-2 px-4 rounded hover:bg-indigo-700 transition duration-300">
                                    Book a Trip
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mt-6">
                        <?php echo e($bookings->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\BooknGo\resources\views/bookings/index.blade.php ENDPATH**/ ?>